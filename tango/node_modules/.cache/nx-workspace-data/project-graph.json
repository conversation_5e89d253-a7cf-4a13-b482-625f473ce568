{"nodes": {"@music163/tango-setting-form": {"name": "@music163/tango-setting-form", "type": "lib", "data": {"root": "packages/setting-form", "sourceRoot": "packages/setting-form", "name": "@music163/tango-setting-form", "tags": ["npm:public"], "metadata": {"targetGroups": {"NPM Scripts": ["clean", "build", "build:esm", "build:cjs", "prepublishOnly"]}, "description": "setting form of tango-apps"}, "targets": {"clean": {"executor": "nx:run-script", "options": {"script": "clean"}, "metadata": {"scriptContent": "rimraf dist/ && rimraf lib/", "runCommand": "yarn clean"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "yarn clean && yarn build:esm && yarn build:cjs", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "build:esm": {"executor": "nx:run-script", "options": {"script": "build:esm"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "runCommand": "yarn build:esm"}, "configurations": {}, "parallelism": true}, "build:cjs": {"executor": "nx:run-script", "options": {"script": "build:cjs"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "runCommand": "yarn build:cjs"}, "configurations": {}, "parallelism": true}, "prepublishOnly": {"executor": "nx:run-script", "options": {"script": "prepublishOnly"}, "metadata": {"scriptContent": "yarn build", "runCommand": "yarn prepublishOnly"}, "configurations": {}, "parallelism": true}, "nx-release-publish": {"dependsOn": ["^nx-release-publish"], "executor": "@nx/js:release-publish", "options": {}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@music163/tango-designer": {"name": "@music163/tango-designer", "type": "lib", "data": {"root": "packages/designer", "sourceRoot": "packages/designer", "name": "@music163/tango-designer", "tags": ["npm:public", "npm:react"], "metadata": {"targetGroups": {"NPM Scripts": ["clean", "build", "build:esm", "build:cjs", "prepublishOnly"]}, "description": "lowcode designer"}, "targets": {"clean": {"executor": "nx:run-script", "options": {"script": "clean"}, "metadata": {"scriptContent": "rimraf lib/", "runCommand": "yarn clean"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "yarn clean && yarn build:esm && yarn build:cjs", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "build:esm": {"executor": "nx:run-script", "options": {"script": "build:esm"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "runCommand": "yarn build:esm"}, "configurations": {}, "parallelism": true}, "build:cjs": {"executor": "nx:run-script", "options": {"script": "build:cjs"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "runCommand": "yarn build:cjs"}, "configurations": {}, "parallelism": true}, "prepublishOnly": {"executor": "nx:run-script", "options": {"script": "prepublishOnly"}, "metadata": {"scriptContent": "yarn build", "runCommand": "yarn prepublishOnly"}, "configurations": {}, "parallelism": true}, "nx-release-publish": {"dependsOn": ["^nx-release-publish"], "executor": "@nx/js:release-publish", "options": {}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@music163/tango-context": {"name": "@music163/tango-context", "type": "lib", "data": {"root": "packages/context", "sourceRoot": "packages/context", "name": "@music163/tango-context", "tags": ["npm:public", "npm:react", "npm:hooks"], "metadata": {"targetGroups": {"NPM Scripts": ["clean", "build", "build:esm", "build:cjs", "prepublishOnly"]}, "description": "react context for tango-apps"}, "targets": {"clean": {"executor": "nx:run-script", "options": {"script": "clean"}, "metadata": {"scriptContent": "rimraf lib/", "runCommand": "yarn clean"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "yarn clean && yarn build:esm && yarn build:cjs", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "build:esm": {"executor": "nx:run-script", "options": {"script": "build:esm"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "runCommand": "yarn build:esm"}, "configurations": {}, "parallelism": true}, "build:cjs": {"executor": "nx:run-script", "options": {"script": "build:cjs"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "runCommand": "yarn build:cjs"}, "configurations": {}, "parallelism": true}, "prepublishOnly": {"executor": "nx:run-script", "options": {"script": "prepublishOnly"}, "metadata": {"scriptContent": "yarn build", "runCommand": "yarn prepublishOnly"}, "configurations": {}, "parallelism": true}, "nx-release-publish": {"dependsOn": ["^nx-release-publish"], "executor": "@nx/js:release-publish", "options": {}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@music163/tango-helpers": {"name": "@music163/tango-helpers", "type": "lib", "data": {"root": "packages/helpers", "sourceRoot": "packages/helpers", "name": "@music163/tango-helpers", "tags": ["npm:public", "npm:shared", "npm:helper", "npm:utils", "npm:types"], "metadata": {"targetGroups": {"NPM Scripts": ["clean", "build", "build:esm", "build:cjs", "prepublishOnly"]}, "description": "Shared types, helpers, and hooks of tango-apps"}, "targets": {"clean": {"executor": "nx:run-script", "options": {"script": "clean"}, "metadata": {"scriptContent": "rimraf lib/", "runCommand": "yarn clean"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "yarn clean && yarn build:esm && yarn build:cjs", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "build:esm": {"executor": "nx:run-script", "options": {"script": "build:esm"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "runCommand": "yarn build:esm"}, "configurations": {}, "parallelism": true}, "build:cjs": {"executor": "nx:run-script", "options": {"script": "build:cjs"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "runCommand": "yarn build:cjs"}, "configurations": {}, "parallelism": true}, "prepublishOnly": {"executor": "nx:run-script", "options": {"script": "prepublishOnly"}, "metadata": {"scriptContent": "yarn build", "runCommand": "yarn prepublishOnly"}, "configurations": {}, "parallelism": true}, "nx-release-publish": {"dependsOn": ["^nx-release-publish"], "executor": "@nx/js:release-publish", "options": {}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@music163/tango-sandbox": {"name": "@music163/tango-sandbox", "type": "lib", "data": {"root": "packages/sandbox", "sourceRoot": "packages/sandbox", "name": "@music163/tango-sandbox", "tags": ["npm:public"], "metadata": {"targetGroups": {"NPM Scripts": ["clean", "build", "build:esm", "build:cjs", "prepublishOnly"]}, "description": "sandbox of tango apps"}, "targets": {"clean": {"executor": "nx:run-script", "options": {"script": "clean"}, "metadata": {"scriptContent": "rimraf dist/ && rimraf lib/", "runCommand": "yarn clean"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "yarn clean && yarn build:esm && yarn build:cjs", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "build:esm": {"executor": "nx:run-script", "options": {"script": "build:esm"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "runCommand": "yarn build:esm"}, "configurations": {}, "parallelism": true}, "build:cjs": {"executor": "nx:run-script", "options": {"script": "build:cjs"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "runCommand": "yarn build:cjs"}, "configurations": {}, "parallelism": true}, "prepublishOnly": {"executor": "nx:run-script", "options": {"script": "prepublishOnly"}, "metadata": {"scriptContent": "yarn build", "runCommand": "yarn prepublishOnly"}, "configurations": {}, "parallelism": true}, "nx-release-publish": {"dependsOn": ["^nx-release-publish"], "executor": "@nx/js:release-publish", "options": {}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "playground": {"name": "playground", "type": "lib", "data": {"root": "apps/playground", "sourceRoot": "apps/playground", "name": "playground", "tags": ["npm:private"], "metadata": {"targetGroups": {"NPM Scripts": ["dev", "build", "postinstall", "setup", "start"]}}, "targets": {"dev": {"executor": "nx:run-script", "options": {"script": "dev"}, "metadata": {"scriptContent": "cross-env HOST=local.netease.com PORT=8001 umi dev", "runCommand": "yarn dev"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "umi build", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "postinstall": {"executor": "nx:run-script", "options": {"script": "postinstall"}, "metadata": {"scriptContent": "umi setup", "runCommand": "yarn postinstall"}, "configurations": {}, "parallelism": true}, "setup": {"executor": "nx:run-script", "options": {"script": "setup"}, "metadata": {"scriptContent": "umi setup", "runCommand": "yarn setup"}, "configurations": {}, "parallelism": true}, "start": {"executor": "nx:run-script", "options": {"script": "start"}, "metadata": {"scriptContent": "npm run dev", "runCommand": "yarn start"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "storybook": {"name": "storybook", "type": "lib", "data": {"root": "apps/storybook", "sourceRoot": "apps/storybook", "name": "storybook", "tags": ["npm:private"], "metadata": {"targetGroups": {"NPM Scripts": ["build", "build-storybook", "storybook"]}, "description": "> tango-apps docs"}, "targets": {"build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "echo \"skip\"", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "build-storybook": {"executor": "nx:run-script", "options": {"script": "build-storybook"}, "metadata": {"scriptContent": "build-storybook", "runCommand": "yarn build-storybook"}, "configurations": {}, "parallelism": true}, "storybook": {"executor": "nx:run-script", "options": {"script": "storybook"}, "metadata": {"scriptContent": "start-storybook -p 6008", "runCommand": "yarn storybook"}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@music163/tango-core": {"name": "@music163/tango-core", "type": "lib", "data": {"root": "packages/core", "sourceRoot": "packages/core", "name": "@music163/tango-core", "tags": ["npm:public"], "metadata": {"targetGroups": {"NPM Scripts": ["clean", "build", "build:esm", "build:cjs", "prepublishOnly"]}, "description": "tango core"}, "targets": {"clean": {"executor": "nx:run-script", "options": {"script": "clean"}, "metadata": {"scriptContent": "rimraf lib/", "runCommand": "yarn clean"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "yarn clean && yarn build:esm && yarn build:cjs", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "build:esm": {"executor": "nx:run-script", "options": {"script": "build:esm"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "runCommand": "yarn build:esm"}, "configurations": {}, "parallelism": true}, "build:cjs": {"executor": "nx:run-script", "options": {"script": "build:cjs"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "runCommand": "yarn build:cjs"}, "configurations": {}, "parallelism": true}, "prepublishOnly": {"executor": "nx:run-script", "options": {"script": "prepublishOnly"}, "metadata": {"scriptContent": "yarn build", "runCommand": "yarn prepublishOnly"}, "configurations": {}, "parallelism": true}, "nx-release-publish": {"dependsOn": ["^nx-release-publish"], "executor": "@nx/js:release-publish", "options": {}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "tango-spec": {"name": "tango-spec", "type": "lib", "data": {"root": "packages/spec", "sourceRoot": "packages/spec", "name": "tango-spec", "tags": ["npm:public", "npm:json", "npm:schema"], "metadata": {"targetGroups": {"NPM Scripts": ["test"]}, "description": "tango config spec"}, "targets": {"test": {"executor": "nx:run-script", "options": {"script": "test"}, "metadata": {"scriptContent": "node ./__tests__/spec.test.js", "runCommand": "yarn test"}, "configurations": {}, "parallelism": true}, "nx-release-publish": {"dependsOn": ["^nx-release-publish"], "executor": "@nx/js:release-publish", "options": {}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}, "@music163/tango-ui": {"name": "@music163/tango-ui", "type": "lib", "data": {"root": "packages/ui", "sourceRoot": "packages/ui", "name": "@music163/tango-ui", "tags": ["npm:public", "npm:react", "npm:ui", "npm:widgets"], "metadata": {"targetGroups": {"NPM Scripts": ["clean", "build", "build:esm", "build:cjs", "prepublishOnly"]}, "description": "ui widgets of tango"}, "targets": {"clean": {"executor": "nx:run-script", "options": {"script": "clean"}, "metadata": {"scriptContent": "rimraf lib/", "runCommand": "yarn clean"}, "configurations": {}, "parallelism": true}, "build": {"executor": "nx:run-script", "options": {"script": "build"}, "metadata": {"scriptContent": "yarn clean && yarn build:esm && yarn build:cjs", "runCommand": "yarn build"}, "configurations": {}, "parallelism": true}, "build:esm": {"executor": "nx:run-script", "options": {"script": "build:esm"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/esm/ --module ES2020", "runCommand": "yarn build:esm"}, "configurations": {}, "parallelism": true}, "build:cjs": {"executor": "nx:run-script", "options": {"script": "build:cjs"}, "metadata": {"scriptContent": "tsc --project tsconfig.prod.json --outDir lib/cjs/ --module CommonJS", "runCommand": "yarn build:cjs"}, "configurations": {}, "parallelism": true}, "prepublishOnly": {"executor": "nx:run-script", "options": {"script": "prepublishOnly"}, "metadata": {"scriptContent": "yarn build", "runCommand": "yarn prepublishOnly"}, "configurations": {}, "parallelism": true}, "nx-release-publish": {"dependsOn": ["^nx-release-publish"], "executor": "@nx/js:release-publish", "options": {}, "configurations": {}, "parallelism": true}}, "implicitDependencies": []}}}, "externalNodes": {}, "dependencies": {"@music163/tango-setting-form": [{"source": "@music163/tango-setting-form", "target": "@music163/tango-core", "type": "static"}, {"source": "@music163/tango-setting-form", "target": "@music163/tango-helpers", "type": "static"}, {"source": "@music163/tango-setting-form", "target": "@music163/tango-ui", "type": "static"}], "@music163/tango-designer": [{"source": "@music163/tango-designer", "target": "@music163/tango-context", "type": "static"}, {"source": "@music163/tango-designer", "target": "@music163/tango-core", "type": "static"}, {"source": "@music163/tango-designer", "target": "@music163/tango-helpers", "type": "static"}, {"source": "@music163/tango-designer", "target": "@music163/tango-sandbox", "type": "static"}, {"source": "@music163/tango-designer", "target": "@music163/tango-setting-form", "type": "static"}, {"source": "@music163/tango-designer", "target": "@music163/tango-ui", "type": "static"}], "@music163/tango-context": [{"source": "@music163/tango-context", "target": "@music163/tango-core", "type": "static"}, {"source": "@music163/tango-context", "target": "@music163/tango-helpers", "type": "static"}], "@music163/tango-helpers": [], "@music163/tango-sandbox": [{"source": "@music163/tango-sandbox", "target": "@music163/tango-core", "type": "static"}, {"source": "@music163/tango-sandbox", "target": "@music163/tango-helpers", "type": "static"}], "playground": [], "storybook": [{"source": "storybook", "target": "@music163/tango-setting-form", "type": "static"}, {"source": "storybook", "target": "@music163/tango-ui", "type": "static"}], "@music163/tango-core": [{"source": "@music163/tango-core", "target": "@music163/tango-helpers", "type": "static"}], "tango-spec": [], "@music163/tango-ui": [{"source": "@music163/tango-ui", "target": "@music163/tango-helpers", "type": "static"}]}, "version": "6.0"}