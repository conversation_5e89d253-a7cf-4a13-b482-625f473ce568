{"version": "6.0", "nxVersion": "19.7.0", "deps": {"@babel/preset-env": "^7.25.4", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@commitlint/cli": "^18.4.3", "@commitlint/config-conventional": "^18.4.3", "@types/jest": "^29.5.7", "@types/lodash-es": "^4.17.10", "@types/lodash.get": "^4.4.8", "@types/lodash.isequal": "^4.5.7", "@types/lodash.set": "^4.3.8", "@types/react": "^18.2.34", "@types/react-dom": "^18.2.14", "@types/styled-components": "^5.1.29", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "conventional-changelog-cli": "^4.1.0", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-config-ali": "^14.0.2", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "http-server": "^14.1.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lerna": "^8.1.2", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "react": "^17.0.0", "react-dom": "^17.0.0", "styled-components": "^5.3.6", "typedoc": "^0.26.7", "typescript": "^5.3.3"}, "pathMappings": {"@music163/tango-helpers": ["packages/helpers/src/index.ts"], "@music163/tango-core": ["packages/core/src/index.ts"], "@music163/tango-context": ["packages/context/src/index.ts"], "@music163/tango-ui": ["packages/ui/src/index.ts"], "@music163/tango-sandbox": ["packages/sandbox/src/index.ts"], "@music163/tango-setting-form": ["packages/setting-form/src/index.ts"], "@music163/tango-designer": ["packages/designer/src/index.ts"]}, "nxJsonPlugins": [], "fileMap": {"projectFileMap": {"@music163/tango-helpers": [{"file": "packages/helpers/CHANGELOG.md", "hash": "8938506324597239239"}, {"file": "packages/helpers/README.md", "hash": "3372879431796707490"}, {"file": "packages/helpers/package.json", "hash": "18123340416079031386"}, {"file": "packages/helpers/src/helpers/array.ts", "hash": "8223151617483272490"}, {"file": "packages/helpers/src/helpers/assert.ts", "hash": "3184896542497409287"}, {"file": "packages/helpers/src/helpers/code-helper.ts", "hash": "8557641354189992655"}, {"file": "packages/helpers/src/helpers/constants.ts", "hash": "1802391504239223108"}, {"file": "packages/helpers/src/helpers/dom.ts", "hash": "4941692861235744095"}, {"file": "packages/helpers/src/helpers/enums.ts", "hash": "13437729136224325538"}, {"file": "packages/helpers/src/helpers/events.ts", "hash": "17303622189168121225"}, {"file": "packages/helpers/src/helpers/function.ts", "hash": "2302664411606233842"}, {"file": "packages/helpers/src/helpers/index.ts", "hash": "5160725249656090955"}, {"file": "packages/helpers/src/helpers/logger.ts", "hash": "14194870722509162727"}, {"file": "packages/helpers/src/helpers/object.ts", "hash": "3745932896148311555"}, {"file": "packages/helpers/src/helpers/react-helper.ts", "hash": "14911226943673453415"}, {"file": "packages/helpers/src/helpers/string.ts", "hash": "7007550375839955371"}, {"file": "packages/helpers/src/hoc/compose.ts", "hash": "13287775234703434682"}, {"file": "packages/helpers/src/hoc/index.ts", "hash": "13257783714718082499"}, {"file": "packages/helpers/src/hoc/with-dnd.tsx", "hash": "628800410512791029"}, {"file": "packages/helpers/src/hooks/index.ts", "hash": "15193078806453466118"}, {"file": "packages/helpers/src/hooks/use-boolean.ts", "hash": "18336688929839251986"}, {"file": "packages/helpers/src/hooks/use-callback-ref.ts", "hash": "9911483598466434453"}, {"file": "packages/helpers/src/hooks/use-controllable.ts", "hash": "7293971166200745630"}, {"file": "packages/helpers/src/index.ts", "hash": "9789103903329961809"}, {"file": "packages/helpers/src/stores/index.ts", "hash": "3181940957212525013"}, {"file": "packages/helpers/src/stores/list-store.ts", "hash": "5961504803028059926"}, {"file": "packages/helpers/src/types/advanced.ts", "hash": "12919285946133970422"}, {"file": "packages/helpers/src/types/base.ts", "hash": "10560894136324972920"}, {"file": "packages/helpers/src/types/index.ts", "hash": "14925045754159026019"}, {"file": "packages/helpers/src/types/prototype.ts", "hash": "8085645364052093267"}, {"file": "packages/helpers/tests/assert.test.ts", "hash": "3045831999785757902"}, {"file": "packages/helpers/tests/code-helper.test.ts", "hash": "7313889358771256040"}, {"file": "packages/helpers/tests/helpers.test.ts", "hash": "6791896403661072953"}, {"file": "packages/helpers/tsconfig.json", "hash": "1419932046348638282"}, {"file": "packages/helpers/tsconfig.prod.json", "hash": "15973848687403907078"}, {"file": "packages/helpers/typedoc.json", "hash": "12344270262483645930"}, {"file": "packages/helpers/yarn-error.log", "hash": "6247531221615332325"}], "@music163/tango-designer": [{"file": "packages/designer/CHANGELOG.md", "hash": "11665824588666434785"}, {"file": "packages/designer/README.md", "hash": "5178024720684613830"}, {"file": "packages/designer/package.json", "hash": "13883810987251641498", "deps": ["@music163/tango-context", "@music163/tango-core", "@music163/tango-helpers", "@music163/tango-sandbox", "@music163/tango-setting-form", "@music163/tango-ui"]}, {"file": "packages/designer/src/components/components-popover.tsx", "hash": "2180229574933291108"}, {"file": "packages/designer/src/components/context-menu.tsx", "hash": "13501231079608162319"}, {"file": "packages/designer/src/components/drag-box.tsx", "hash": "1895664705181956837"}, {"file": "packages/designer/src/components/file-errors-overlay.tsx", "hash": "2913106294583347379"}, {"file": "packages/designer/src/components/index.ts", "hash": "3890611661481448529"}, {"file": "packages/designer/src/components/input-kv.tsx", "hash": "1987757031769834944"}, {"file": "packages/designer/src/components/menu.tsx", "hash": "12182622385503676377"}, {"file": "packages/designer/src/components/variable-tree-modal.tsx", "hash": "8142741736730915411"}, {"file": "packages/designer/src/components/variable-tree/add-service.tsx", "hash": "4015458019382273330"}, {"file": "packages/designer/src/components/variable-tree/add-store.tsx", "hash": "2360450185626226013"}, {"file": "packages/designer/src/components/variable-tree/index.tsx", "hash": "13738713900730180412"}, {"file": "packages/designer/src/components/variable-tree/service-preview.tsx", "hash": "6348344820244239448"}, {"file": "packages/designer/src/components/variable-tree/value-detail.tsx", "hash": "12759646766233715760"}, {"file": "packages/designer/src/components/variable-tree/value-preview.tsx", "hash": "3414709754162556335"}, {"file": "packages/designer/src/context-menu/copy-node.tsx", "hash": "16206788046444911805"}, {"file": "packages/designer/src/context-menu/delete-node.tsx", "hash": "6881511493348354298"}, {"file": "packages/designer/src/context-menu/index.tsx", "hash": "13971764669645152607"}, {"file": "packages/designer/src/context-menu/paste-node.tsx", "hash": "4091608745647249806"}, {"file": "packages/designer/src/context-menu/view-source.tsx", "hash": "17579359259073473303"}, {"file": "packages/designer/src/context.ts", "hash": "6931110759300182941"}, {"file": "packages/designer/src/designer-panel.tsx", "hash": "11607966421016327095"}, {"file": "packages/designer/src/designer.tsx", "hash": "16725490396799491079"}, {"file": "packages/designer/src/dnd/dnd-query.ts", "hash": "9062556232727297377"}, {"file": "packages/designer/src/dnd/hotkey.ts", "hash": "15490923824062946982"}, {"file": "packages/designer/src/dnd/index.ts", "hash": "11368016475672814639"}, {"file": "packages/designer/src/dnd/use-dnd.ts", "hash": "4385403290927828693"}, {"file": "packages/designer/src/editor.tsx", "hash": "8187135058693308750"}, {"file": "packages/designer/src/helpers/dom.ts", "hash": "6880117616398681185"}, {"file": "packages/designer/src/helpers/index.ts", "hash": "16829420663339183469"}, {"file": "packages/designer/src/helpers/template.ts", "hash": "2750986277797830749"}, {"file": "packages/designer/src/index.ts", "hash": "12796815750375903224"}, {"file": "packages/designer/src/sandbox/index.ts", "hash": "12098161315981480533"}, {"file": "packages/designer/src/sandbox/navigator.tsx", "hash": "11163831915786649411"}, {"file": "packages/designer/src/sandbox/sandbox.tsx", "hash": "6028694377022896070"}, {"file": "packages/designer/src/selection-menu/copy-node.tsx", "hash": "11888678688127981465"}, {"file": "packages/designer/src/selection-menu/delete-node.tsx", "hash": "11712977293224576766"}, {"file": "packages/designer/src/selection-menu/index.ts", "hash": "91921368074821144"}, {"file": "packages/designer/src/selection-menu/more-actions.tsx", "hash": "13438802044834215955"}, {"file": "packages/designer/src/selection-menu/select-parent-node.tsx", "hash": "15240999760524434382"}, {"file": "packages/designer/src/selection-menu/view-source.tsx", "hash": "6655066631370696477"}, {"file": "packages/designer/src/setters/action-list-setter.tsx", "hash": "5188384440587454386"}, {"file": "packages/designer/src/setters/choice-setter.tsx", "hash": "6021963275024359018"}, {"file": "packages/designer/src/setters/classname-setter.tsx", "hash": "9177740454081496253"}, {"file": "packages/designer/src/setters/code-setter.tsx", "hash": "592277191344819316"}, {"file": "packages/designer/src/setters/column-setter.tsx", "hash": "16809490965081747759"}, {"file": "packages/designer/src/setters/css-setter.tsx", "hash": "1626388662502760250"}, {"file": "packages/designer/src/setters/date-setter.tsx", "hash": "7824106058549523484"}, {"file": "packages/designer/src/setters/enum-setter.tsx", "hash": "5578098981058060274"}, {"file": "packages/designer/src/setters/event-setter.tsx", "hash": "4451075246022927010"}, {"file": "packages/designer/src/setters/index.ts", "hash": "6663754543169481300"}, {"file": "packages/designer/src/setters/json-setter.tsx", "hash": "1282661098912240201"}, {"file": "packages/designer/src/setters/jsx-setter.tsx", "hash": "17629585291126903095"}, {"file": "packages/designer/src/setters/list-setter.tsx", "hash": "12229425144214083179"}, {"file": "packages/designer/src/setters/model-setter.tsx", "hash": "10932542987244131362"}, {"file": "packages/designer/src/setters/option-setter.tsx", "hash": "9429433904581022834"}, {"file": "packages/designer/src/setters/picker-setter.tsx", "hash": "12129456029146617620"}, {"file": "packages/designer/src/setters/render-setter.tsx", "hash": "5662876619713342975"}, {"file": "packages/designer/src/setters/router-setter.tsx", "hash": "8766665126068183079"}, {"file": "packages/designer/src/setters/style-setter.tsx", "hash": "13998904641700395936"}, {"file": "packages/designer/src/setting-panel.tsx", "hash": "4017020660786039392"}, {"file": "packages/designer/src/sidebar/components-panel.tsx", "hash": "6347762766175074675"}, {"file": "packages/designer/src/sidebar/datasource-panel/index.tsx", "hash": "16603272472152161411"}, {"file": "packages/designer/src/sidebar/datasource-panel/interface-config.tsx", "hash": "17161512878663550519"}, {"file": "packages/designer/src/sidebar/datasource-panel/proxy-config.tsx", "hash": "4476930735881498270"}, {"file": "packages/designer/src/sidebar/dependency-panel.tsx", "hash": "9636126286179847040"}, {"file": "packages/designer/src/sidebar/history-panel.tsx", "hash": "6164054112447474372"}, {"file": "packages/designer/src/sidebar/index.ts", "hash": "10364107101236252277"}, {"file": "packages/designer/src/sidebar/outline-panel/components-tree.tsx", "hash": "14626730084143823484"}, {"file": "packages/designer/src/sidebar/outline-panel/index.tsx", "hash": "9337879765020450103"}, {"file": "packages/designer/src/sidebar/outline-panel/state-tree.tsx", "hash": "9652588355023087471"}, {"file": "packages/designer/src/sidebar/resizable-box.tsx", "hash": "16307570065575878311"}, {"file": "packages/designer/src/sidebar/sidebar.tsx", "hash": "13768099787352703881"}, {"file": "packages/designer/src/sidebar/variable-panel.tsx", "hash": "4477208926665795872"}, {"file": "packages/designer/src/simulator/bottom-bar.tsx", "hash": "9937657415066828620"}, {"file": "packages/designer/src/simulator/error-boundary.tsx", "hash": "14705968359066536270"}, {"file": "packages/designer/src/simulator/ghost.tsx", "hash": "7296112198685702999"}, {"file": "packages/designer/src/simulator/index.tsx", "hash": "11835748736806820663"}, {"file": "packages/designer/src/simulator/insertion.tsx", "hash": "3741660182091693707"}, {"file": "packages/designer/src/simulator/mask.tsx", "hash": "10424182311360742097"}, {"file": "packages/designer/src/simulator/selection-mask.tsx", "hash": "10134238246433948578"}, {"file": "packages/designer/src/simulator/selection.tsx", "hash": "14016084266946883863"}, {"file": "packages/designer/src/simulator/simulator.tsx", "hash": "13135605223474545799"}, {"file": "packages/designer/src/simulator/viewport.tsx", "hash": "5343556593880563621"}, {"file": "packages/designer/src/themes/default.ts", "hash": "735905799886539113"}, {"file": "packages/designer/src/themes/index.ts", "hash": "1242409085478697194"}, {"file": "packages/designer/src/themes/light.ts", "hash": "15409839754662192663"}, {"file": "packages/designer/src/toolbar/history.tsx", "hash": "11079391883008755101"}, {"file": "packages/designer/src/toolbar/index.ts", "hash": "13657417727475266830"}, {"file": "packages/designer/src/toolbar/mode-switch.tsx", "hash": "8216725512883359063"}, {"file": "packages/designer/src/toolbar/preview.tsx", "hash": "14115932713368357645"}, {"file": "packages/designer/src/toolbar/route-switch.tsx", "hash": "8831832875719520594"}, {"file": "packages/designer/src/toolbar/toggle-panel.tsx", "hash": "3747233233628916631"}, {"file": "packages/designer/src/toolbar/toolbar.tsx", "hash": "1659644181281210392"}, {"file": "packages/designer/src/toolbar/viewport-switch.tsx", "hash": "17006524643411008773"}, {"file": "packages/designer/src/types/index.ts", "hash": "1466596696380855143"}, {"file": "packages/designer/src/widgets.ts", "hash": "4625880598336194824"}, {"file": "packages/designer/src/workspace-panel.tsx", "hash": "934066111663825216"}, {"file": "packages/designer/src/workspace-view.tsx", "hash": "12723478233300484608"}, {"file": "packages/designer/tsconfig.json", "hash": "1419932046348638282"}, {"file": "packages/designer/tsconfig.prod.json", "hash": "15973848687403907078"}, {"file": "packages/designer/typedoc.json", "hash": "12344270262483645930"}], "@music163/tango-context": [{"file": "packages/context/CHANGELOG.md", "hash": "8566222026703134509"}, {"file": "packages/context/README.md", "hash": "6013410926249014124"}, {"file": "packages/context/package.json", "hash": "12547452111916041567", "deps": ["@music163/tango-core", "@music163/tango-helpers"]}, {"file": "packages/context/src/context.ts", "hash": "5537866760627954738"}, {"file": "packages/context/src/index.ts", "hash": "12375102245951495453"}, {"file": "packages/context/tsconfig.json", "hash": "1419932046348638282"}, {"file": "packages/context/tsconfig.prod.json", "hash": "15973848687403907078"}], "@music163/tango-ui": [{"file": "packages/ui/CHANGELOG.md", "hash": "3993508075421828987"}, {"file": "packages/ui/README.md", "hash": "10769432344378884437"}, {"file": "packages/ui/package.json", "hash": "8939469656766509399", "deps": ["@music163/tango-helpers"]}, {"file": "packages/ui/src/action-select.tsx", "hash": "7078038109183807170"}, {"file": "packages/ui/src/action.tsx", "hash": "11143208289924768424"}, {"file": "packages/ui/src/chat-input.tsx", "hash": "4728326459032681530"}, {"file": "packages/ui/src/classname-input.tsx", "hash": "12530456428254406923"}, {"file": "packages/ui/src/code-editor.tsx", "hash": "4932375679617223392"}, {"file": "packages/ui/src/collapse-panel.tsx", "hash": "11484369408857505553"}, {"file": "packages/ui/src/color-tag.tsx", "hash": "4558126474669885577"}, {"file": "packages/ui/src/config-form.tsx", "hash": "11055170848374001922"}, {"file": "packages/ui/src/context-action.tsx", "hash": "14126723915390471281"}, {"file": "packages/ui/src/copy-clipboard.tsx", "hash": "1668555203421422293"}, {"file": "packages/ui/src/drag-panel.tsx", "hash": "17187572666005925916"}, {"file": "packages/ui/src/error-boundary.tsx", "hash": "5180970405594112408"}, {"file": "packages/ui/src/file-explorer/directory.tsx", "hash": "13553542893416618527"}, {"file": "packages/ui/src/file-explorer/file.tsx", "hash": "2820558644520579671"}, {"file": "packages/ui/src/file-explorer/index.ts", "hash": "5674103027135106406"}, {"file": "packages/ui/src/file-explorer/module-list.tsx", "hash": "11760712188578514212"}, {"file": "packages/ui/src/iconfont.tsx", "hash": "207742825828454690"}, {"file": "packages/ui/src/icons/code-outlined.tsx", "hash": "6168571278140498031"}, {"file": "packages/ui/src/icons/create-icon.tsx", "hash": "11459939506834185726"}, {"file": "packages/ui/src/icons/dual-outlined.tsx", "hash": "11954379922654241052"}, {"file": "packages/ui/src/icons/index.ts", "hash": "14526792112742961374"}, {"file": "packages/ui/src/icons/line-dashed-outlined.tsx", "hash": "5158084421330062150"}, {"file": "packages/ui/src/icons/line-solid-outlined.tsx", "hash": "2238885663865851815"}, {"file": "packages/ui/src/icons/open-panel-filled-left-outlined.tsx", "hash": "9087896814378420356"}, {"file": "packages/ui/src/icons/open-panel-filled-right-outlined.tsx", "hash": "14248694546746509584"}, {"file": "packages/ui/src/icons/open-panel-left-outlined.tsx", "hash": "15805708613062573859"}, {"file": "packages/ui/src/icons/open-panel-right-outlined.tsx", "hash": "16898093785883069413"}, {"file": "packages/ui/src/icons/package-outlined.tsx", "hash": "4830459873729578288"}, {"file": "packages/ui/src/icons/pop-out-outlined.tsx", "hash": "8308371203494928161"}, {"file": "packages/ui/src/icons/redo-outlined.tsx", "hash": "2354066451802886488"}, {"file": "packages/ui/src/icons/undo-outlined.tsx", "hash": "9787508994732552711"}, {"file": "packages/ui/src/index.ts", "hash": "6967018481054986996"}, {"file": "packages/ui/src/input-code.tsx", "hash": "3094991100379626842"}, {"file": "packages/ui/src/input-list.tsx", "hash": "10702344141623409550"}, {"file": "packages/ui/src/input-style-code.tsx", "hash": "10194246319660885236"}, {"file": "packages/ui/src/json-view.tsx", "hash": "633691406228786089"}, {"file": "packages/ui/src/lang/css-object.ts", "hash": "16388895999202769476"}, {"file": "packages/ui/src/menu.tsx", "hash": "4505801278543855889"}, {"file": "packages/ui/src/panel.tsx", "hash": "16714948543038242394"}, {"file": "packages/ui/src/popover.tsx", "hash": "8316905519534735056"}, {"file": "packages/ui/src/search.tsx", "hash": "4489219095369596852"}, {"file": "packages/ui/src/select-action.tsx", "hash": "5395349579395855816"}, {"file": "packages/ui/src/select-list.tsx", "hash": "5599254746379117918"}, {"file": "packages/ui/src/tabs.tsx", "hash": "12090906740125852202"}, {"file": "packages/ui/src/tag-select.tsx", "hash": "17388899954407146595"}, {"file": "packages/ui/src/toggle-button.tsx", "hash": "17057481792514649781"}, {"file": "packages/ui/tsconfig.json", "hash": "1419932046348638282"}, {"file": "packages/ui/tsconfig.prod.json", "hash": "15973848687403907078"}], "tango-spec": [{"file": "packages/spec/CHANGELOG.md", "hash": "551982652485356530"}, {"file": "packages/spec/README.md", "hash": "8816399430349237842"}, {"file": "packages/spec/package.json", "hash": "3383022931090864606"}, {"file": "packages/spec/tango-config.json", "hash": "13791608963300499716"}], "playground": [{"file": "apps/playground/.gitignore", "hash": "14703162118380517911"}, {"file": "apps/playground/.umirc.ts", "hash": "2147277436086565502"}, {"file": "apps/playground/package.json", "hash": "8022101765268792127"}, {"file": "apps/playground/plugin.ts", "hash": "958329399084698716"}, {"file": "apps/playground/src/assets/yay.jpg", "hash": "795963674844490368"}, {"file": "apps/playground/src/components/foo-setter.tsx", "hash": "11500641008651893883"}, {"file": "apps/playground/src/components/index.ts", "hash": "16938250145431571791"}, {"file": "apps/playground/src/components/other-panel.tsx", "hash": "14147178829739234252"}, {"file": "apps/playground/src/helpers/index.tsx", "hash": "16128522893522376585"}, {"file": "apps/playground/src/helpers/mail-files.ts", "hash": "16734268484316169117"}, {"file": "apps/playground/src/helpers/mock-files.ts", "hash": "16893892625559209986"}, {"file": "apps/playground/src/helpers/prototypes.ts", "hash": "17096257597774083277"}, {"file": "apps/playground/src/layouts/index.less", "hash": "8501940535200414390"}, {"file": "apps/playground/src/layouts/index.tsx", "hash": "14244947603561511056"}, {"file": "apps/playground/src/pages/docs.tsx", "hash": "16034430383257268935"}, {"file": "apps/playground/src/pages/index.tsx", "hash": "570819763875264544"}, {"file": "apps/playground/src/pages/mail.tsx", "hash": "11447437799137985505"}, {"file": "apps/playground/tsconfig.json", "hash": "9749882762862528685"}, {"file": "apps/playground/typings.d.ts", "hash": "10777899963690398430"}], "storybook": [{"file": "apps/storybook/.storybook/main.js", "hash": "646545845449643453"}, {"file": "apps/storybook/.storybook/preview-head.html", "hash": "8577239984228619457"}, {"file": "apps/storybook/.storybook/preview.js", "hash": "6344959810230275071"}, {"file": "apps/storybook/README.md", "hash": "13085148975540868268"}, {"file": "apps/storybook/package.json", "hash": "1326228574635860222", "deps": ["@music163/tango-setting-form", "@music163/tango-ui"]}, {"file": "apps/storybook/src/editor.stories.tsx", "hash": "5803407919218356171"}, {"file": "apps/storybook/src/sandbox.stories.tsx", "hash": "12756913160912553854"}, {"file": "apps/storybook/src/setting-form.stories.tsx", "hash": "8057261762757731695"}, {"file": "apps/storybook/src/ui/action-select.stories.tsx", "hash": "2776957832145919484"}, {"file": "apps/storybook/src/ui/action.stories.tsx", "hash": "17037694768537607647"}, {"file": "apps/storybook/src/ui/chat.stories.tsx", "hash": "7832854680989182548"}, {"file": "apps/storybook/src/ui/classname-input.stories.tsx", "hash": "67714004459796755"}, {"file": "apps/storybook/src/ui/copy.stories.tsx", "hash": "4600417395075800381"}, {"file": "apps/storybook/src/ui/drag-panel.stories.tsx", "hash": "2380902828291589773"}, {"file": "apps/storybook/src/ui/input-code.stories.tsx", "hash": "8643765231672194990"}, {"file": "apps/storybook/src/ui/input-list.stories.tsx", "hash": "2734728863350202506"}, {"file": "apps/storybook/src/ui/menu.stories.tsx", "hash": "10805232783526123892"}, {"file": "apps/storybook/src/ui/select-list.stories.tsx", "hash": "13600754693801152553"}, {"file": "apps/storybook/src/ui/tag-select.stories.tsx", "hash": "7068988841212184541"}, {"file": "apps/storybook/src/ui/toggle-button.stories.tsx", "hash": "14621476888722464046"}, {"file": "apps/storybook/src/ui/var-tree.stories.tsx", "hash": "665575328776601593"}, {"file": "apps/storybook/tsconfig.json", "hash": "18143460398965180952"}, {"file": "apps/storybook/yarn-error.log", "hash": "18394725223917376495"}], "@music163/tango-core": [{"file": "packages/core/CHANGELOG.md", "hash": "3900041829612072385"}, {"file": "packages/core/README.md", "hash": "2214032600415010574"}, {"file": "packages/core/package.json", "hash": "7386429252586509986", "deps": ["@music163/tango-helpers"]}, {"file": "packages/core/src/factory.ts", "hash": "17614974511756876776"}, {"file": "packages/core/src/helpers/assert.ts", "hash": "5364653012466786967"}, {"file": "packages/core/src/helpers/ast/generate.ts", "hash": "13548817062112324081"}, {"file": "packages/core/src/helpers/ast/index.ts", "hash": "15130976026197101185"}, {"file": "packages/core/src/helpers/ast/parse.ts", "hash": "530790092098688147"}, {"file": "packages/core/src/helpers/ast/traverse.ts", "hash": "14744812923872118507"}, {"file": "packages/core/src/helpers/code-helpers.ts", "hash": "5675475249076763760"}, {"file": "packages/core/src/helpers/id-generator.ts", "hash": "6585035491749806037"}, {"file": "packages/core/src/helpers/index.ts", "hash": "3190210471841236864"}, {"file": "packages/core/src/helpers/object.ts", "hash": "2056841103307727315"}, {"file": "packages/core/src/helpers/prototype.ts", "hash": "2363160981903195992"}, {"file": "packages/core/src/helpers/schema-helpers.ts", "hash": "17498814401375179296"}, {"file": "packages/core/src/helpers/string.ts", "hash": "13784633579427773139"}, {"file": "packages/core/src/index.ts", "hash": "1156811114490082414"}, {"file": "packages/core/src/models/abstract-code-workspace.ts", "hash": "9540162676097563999"}, {"file": "packages/core/src/models/abstract-file.ts", "hash": "3544671595731155778"}, {"file": "packages/core/src/models/abstract-js-file.ts", "hash": "3672192898320282716"}, {"file": "packages/core/src/models/abstract-json-file.ts", "hash": "11205386024675675464"}, {"file": "packages/core/src/models/abstract-view-node.ts", "hash": "2883927674382276563"}, {"file": "packages/core/src/models/abstract-workspace.ts", "hash": "8658826150147254836"}, {"file": "packages/core/src/models/designer.ts", "hash": "10162957474087133745"}, {"file": "packages/core/src/models/drag-source.ts", "hash": "2111056580961184074"}, {"file": "packages/core/src/models/drop-target.ts", "hash": "6447775147692609295"}, {"file": "packages/core/src/models/engine.ts", "hash": "6928227286514200226"}, {"file": "packages/core/src/models/file.ts", "hash": "14717405952552835321"}, {"file": "packages/core/src/models/history.ts", "hash": "7092878459065919416"}, {"file": "packages/core/src/models/index.ts", "hash": "3914676519789984833"}, {"file": "packages/core/src/models/interfaces.ts", "hash": "10062882293281655149"}, {"file": "packages/core/src/models/js-app-entry-file.ts", "hash": "17229735739179006883"}, {"file": "packages/core/src/models/js-file.ts", "hash": "4189468296636512625"}, {"file": "packages/core/src/models/js-local-components-entry-file.ts", "hash": "2156416603725127630"}, {"file": "packages/core/src/models/js-route-config-file.ts", "hash": "9496635701779248533"}, {"file": "packages/core/src/models/js-service-file.ts", "hash": "14368526088233025695"}, {"file": "packages/core/src/models/js-store-entry-file.ts", "hash": "1495135742312040365"}, {"file": "packages/core/src/models/js-store-file.ts", "hash": "4084840396025935161"}, {"file": "packages/core/src/models/js-view-file.ts", "hash": "2636079129000216768"}, {"file": "packages/core/src/models/json-file.ts", "hash": "18433814108773367397"}, {"file": "packages/core/src/models/select-source.ts", "hash": "17681677985287825272"}, {"file": "packages/core/src/models/view-node.ts", "hash": "39212571198681728"}, {"file": "packages/core/src/models/workspace.ts", "hash": "5987318773777508200"}, {"file": "packages/core/src/types.ts", "hash": "3724431551938424538"}, {"file": "packages/core/tests/assert.test.ts", "hash": "8046493954622914238"}, {"file": "packages/core/tests/ast.test.ts", "hash": "16554944296530187656"}, {"file": "packages/core/tests/engine.test.ts", "hash": "4281375530309873267"}, {"file": "packages/core/tests/helpers.test.ts", "hash": "7179201809587808501"}, {"file": "packages/core/tests/proto.test.ts", "hash": "5749751214275771051"}, {"file": "packages/core/tsconfig.json", "hash": "1419932046348638282"}, {"file": "packages/core/tsconfig.prod.json", "hash": "15973848687403907078"}, {"file": "packages/core/typedoc.json", "hash": "12344270262483645930"}], "@music163/tango-setting-form": [{"file": "packages/setting-form/CHANGELOG.md", "hash": "2208961500975315487"}, {"file": "packages/setting-form/README.md", "hash": "8735239409575889932"}, {"file": "packages/setting-form/package.json", "hash": "7269857170899194901", "deps": ["@music163/tango-core", "@music163/tango-helpers", "@music163/tango-ui"]}, {"file": "packages/setting-form/src/context.tsx", "hash": "6056770018796886754"}, {"file": "packages/setting-form/src/form-item.tsx", "hash": "1211292154916860277"}, {"file": "packages/setting-form/src/form-model.tsx", "hash": "11447168977174094974"}, {"file": "packages/setting-form/src/form-object.tsx", "hash": "5912419709451388870"}, {"file": "packages/setting-form/src/form-ui.tsx", "hash": "7873601221158747837"}, {"file": "packages/setting-form/src/form.tsx", "hash": "16863589209884877582"}, {"file": "packages/setting-form/src/helpers.ts", "hash": "5438745987761206254"}, {"file": "packages/setting-form/src/index.ts", "hash": "12933555671785390298"}, {"file": "packages/setting-form/src/setters/bool-setter.tsx", "hash": "8727987991174864519"}, {"file": "packages/setting-form/src/setters/code-setter.tsx", "hash": "11365426507181252891"}, {"file": "packages/setting-form/src/setters/id-setter.tsx", "hash": "15606008421781627185"}, {"file": "packages/setting-form/src/setters/index.ts", "hash": "7674369512674155426"}, {"file": "packages/setting-form/src/setters/number-setter.tsx", "hash": "1488474994606753773"}, {"file": "packages/setting-form/src/setters/register.tsx", "hash": "10879954455682046066"}, {"file": "packages/setting-form/src/setters/text-setter.tsx", "hash": "14201990366286432125"}, {"file": "packages/setting-form/src/types.ts", "hash": "243367834239002539"}, {"file": "packages/setting-form/tsconfig.json", "hash": "1419932046348638282"}, {"file": "packages/setting-form/tsconfig.prod.json", "hash": "15973848687403907078"}, {"file": "packages/setting-form/typedoc.json", "hash": "12344270262483645930"}], "@music163/tango-sandbox": [{"file": "packages/sandbox/CHANGELOG.md", "hash": "4526043359305995587"}, {"file": "packages/sandbox/README.md", "hash": "7140697087297645922"}, {"file": "packages/sandbox/package.json", "hash": "16952323179779473288", "deps": ["@music163/tango-core", "@music163/tango-helpers"]}, {"file": "packages/sandbox/src/code-sandbox/helper.ts", "hash": "18344655680152408193"}, {"file": "packages/sandbox/src/code-sandbox/iframe-protocol.ts", "hash": "11606738021299167699"}, {"file": "packages/sandbox/src/code-sandbox/index.tsx", "hash": "16698525816807547683"}, {"file": "packages/sandbox/src/code-sandbox/loading.tsx", "hash": "3652670728873988716"}, {"file": "packages/sandbox/src/code-sandbox/manager.ts", "hash": "3041067496648018835"}, {"file": "packages/sandbox/src/index.ts", "hash": "10737359499590458491"}, {"file": "packages/sandbox/src/types.ts", "hash": "4780400031378103147"}, {"file": "packages/sandbox/tsconfig.json", "hash": "1419932046348638282"}, {"file": "packages/sandbox/tsconfig.prod.json", "hash": "4591946633651173100"}]}, "nonProjectFiles": [{"file": ".eslintrc", "hash": "1384223178100524147"}, {"file": ".github/workflows/ci.yml", "hash": "3766387411291585377"}, {"file": ".github/workflows/deploy.yml", "hash": "15981427578195161585"}, {"file": ".giti<PERSON>re", "hash": "15680993583187782944"}, {"file": ".husky/commit-msg", "hash": "16396177967328823253"}, {"file": ".husky/pre-commit", "hash": "12781221215060153475"}, {"file": ".npmrc", "hash": "3087809981446070101"}, {"file": ".prettier<PERSON>", "hash": "12215798963846652517"}, {"file": "LICENSE", "hash": "18254285334535895086"}, {"file": "README.md", "hash": "14555489439189276498"}, {"file": "README.zh-CN.md", "hash": "9925698277442144179"}, {"file": "babel.config.js", "hash": "15726384096961423254"}, {"file": "commitlint.config.js", "hash": "9279724180185467925"}, {"file": "jest.config.js", "hash": "17681354814226882663"}, {"file": "lerna.json", "hash": "1097653960803844281"}, {"file": "package-lock.json", "hash": "14425188335328515154"}, {"file": "package.json", "hash": "16302188853564151659"}, {"file": "public/dashboard-builder.png", "hash": "12306447339847447311"}, {"file": "public/mail-builder.png", "hash": "9780038472699434447"}, {"file": "public/rn-builder.png", "hash": "9539477209611060581"}, {"file": "tsconfig.json", "hash": "3368545161106055228"}, {"file": "tsconfig.prod.json", "hash": "15833239558781407986"}, {"file": "typedoc.base.json", "hash": "9878861572529032270"}, {"file": "typedoc.json", "hash": "18099521438783313216"}, {"file": "yarn.lock", "hash": "15752151713074141507"}]}}