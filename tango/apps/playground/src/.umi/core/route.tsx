// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/","parentId":"@@/global-layout","id":"1"},"2":{"path":"/mail","parentId":"@@/global-layout","id":"2"},"3":{"path":"/docs","parentId":"@@/global-layout","id":"3"},"@@/global-layout":{"id":"@@/global-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import(/* webpackChunkName: "p__index" */'@/pages/index.tsx')),
'2': React.lazy(() => import(/* webpackChunkName: "p__mail" */'@/pages/mail.tsx')),
'3': React.lazy(() => import(/* webpackChunkName: "p__docs" */'@/pages/docs.tsx')),
'@@/global-layout': React.lazy(() => import(/* webpackChunkName: "layouts__index" */'/Users/<USER>/0311 tango/tango/apps/playground/src/layouts/index.tsx')),
},
  };
}
