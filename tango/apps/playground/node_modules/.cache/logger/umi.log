{"level":30,"time":1762187509409,"pid":82481,"hostname":"saifeisideMac-Studio.local","msg":"[你知道吗？] 想快速修改 html 模板、DIY 项目？试试编写项目级插件，详见 https://umijs.org/docs/guides/directory-structure#plugints"}
{"level":30,"time":1762187509410,"pid":82481,"hostname":"saifeisideMac-Studio.local","msg":"generate files"}
{"level":30,"time":1762187509700,"pid":82481,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":30,"time":1762187574581,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[33m[你知道吗？] dev 模式下访问 /__umi 路由，可以发现很多有用的内部信息。\u001b[39m"}
{"level":30,"time":1762187574582,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[36m\u001b[1mUmi v4.3.20\u001b[22m\u001b[39m"}
{"level":30,"time":1762187574859,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":55,"time":1762187575341,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"[HTTPS] Starting service in https mode..."}
{"level":60,"time":1762187575342,"pid":83106,"hostname":"saifeisideMac-Studio.local","err":{"type":"Error","message":"ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'","stack":"Error: ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'\n    at readFileSync (node:fs:442:20)\n    at createHttpsServer (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-utils/dist/https.js:114:39)\n    at async createServer (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-webpack/dist/server/server.js:197:14)\n    at async Proxy.dev (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-webpack/dist/dev.js:64:3)\n    at async Command.fn (/Users/<USER>/0311 tango/tango/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js:409:9)\n    at async Service.run (/Users/<USER>/0311 tango/tango/node_modules/@umijs/core/dist/service/service.js:328:15)\n    at async Service.run2 (/Users/<USER>/0311 tango/tango/node_modules/umi/dist/service/service.js:65:12)\n    at async /Users/<USER>/0311 tango/tango/node_modules/umi/dist/cli/forkedDev.js:23:5","errno":-2,"code":"ENOENT","syscall":"open","path":"/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem"},"msg":"ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'"}
{"level":60,"time":1762187575374,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1762187575375,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"/Users/<USER>/0311 tango/tango/apps/playground/node_modules/.cache/logger/umi.log"}
{"level":60,"time":1762187575376,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1762187615612,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[33m[你知道吗？] father 4 正式发布了，详见 https://zhuanlan.zhihu.com/p/558192063\u001b[39m"}
{"level":30,"time":1762187615613,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[36m\u001b[1mUmi v4.3.20\u001b[22m\u001b[39m"}
{"level":30,"time":1762187615883,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":55,"time":1762187616369,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"[HTTPS] Starting service in https mode..."}
{"level":60,"time":1762187616370,"pid":83412,"hostname":"saifeisideMac-Studio.local","err":{"type":"Error","message":"ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'","stack":"Error: ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'\n    at readFileSync (node:fs:442:20)\n    at createHttpsServer (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-utils/dist/https.js:114:39)\n    at async createServer (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-webpack/dist/server/server.js:197:14)\n    at async Proxy.dev (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-webpack/dist/dev.js:64:3)\n    at async Command.fn (/Users/<USER>/0311 tango/tango/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js:409:9)\n    at async Service.run (/Users/<USER>/0311 tango/tango/node_modules/@umijs/core/dist/service/service.js:328:15)\n    at async Service.run2 (/Users/<USER>/0311 tango/tango/node_modules/umi/dist/service/service.js:65:12)\n    at async /Users/<USER>/0311 tango/tango/node_modules/umi/dist/cli/forkedDev.js:23:5","errno":-2,"code":"ENOENT","syscall":"open","path":"/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem"},"msg":"ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'"}
{"level":60,"time":1762187616418,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1762187616419,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"/Users/<USER>/0311 tango/tango/apps/playground/node_modules/.cache/logger/umi.log"}
{"level":60,"time":1762187616421,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1762187658346,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[33m[你知道吗？] 全局数据存储，在 React 之外修改数据怎么办？试试一键上手 valtio，详见 https://umijs.org/docs/max/valtio\u001b[39m"}
{"level":30,"time":1762187658347,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[36m\u001b[1mUmi v4.3.20\u001b[22m\u001b[39m"}
{"level":30,"time":1762187658626,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":31,"time":1762187659126,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://*************:8001\u001b[39m              \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1762187665285,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 6168 ms (4208 modules)"}
{"level":55,"time":1762190859101,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiling..."}
{"level":32,"time":1762190859788,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 653 ms (4193 modules)"}
{"level":55,"time":1762190859808,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiling..."}
{"level":32,"time":1762190860050,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 216 ms (4193 modules)"}
{"level":55,"time":1762190880900,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiling..."}
{"level":30,"time":1762190880893,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1762190880894,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[36m\u001b[1mUmi v4.3.20\u001b[22m\u001b[39m"}
{"level":30,"time":1762190881198,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":31,"time":1762190881910,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://*************:8002\u001b[39m              \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":50,"time":1762190880955,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:9:0-25"}
{"level":50,"time":1762190880955,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:11:0-41"}
{"level":50,"time":1762190880955,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:12:0-52"}
{"level":50,"time":1762190880955,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:13:0-47"}
{"level":50,"time":1762190880955,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:14:0-39"}
{"level":55,"time":1762190883089,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiling..."}
{"level":50,"time":1762190883142,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:9:0-25"}
{"level":50,"time":1762190883142,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:11:0-41"}
{"level":50,"time":1762190883142,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:12:0-52"}
{"level":50,"time":1762190883142,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:13:0-47"}
{"level":50,"time":1762190883142,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:14:0-39"}
{"level":50,"time":1762190883149,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[icons] build failed: Error: Build failed with 1 error:\nsrc/.umi/exports.ts:19:28: ERROR: Could not resolve \"./testBrowser\""}
{"level":32,"time":1762190883404,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 1504 ms (4193 modules)"}
{"level":30,"time":1762191187699,"pid":96034,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[33m[你知道吗？] 如果你需要使用 Jest 来测试 Umi 项目, umi g jest 就可以一键完成配置，详见 https://umijs.org/docs/guides/generator#jest-配置生成器\u001b[39m"}
{"level":30,"time":1762191187700,"pid":96034,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[36m\u001b[1mUmi v4.3.20\u001b[22m\u001b[39m"}
{"level":30,"time":1762191188019,"pid":96034,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":55,"time":1762191187707,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiling..."}
{"level":55,"time":1762191187756,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiling..."}
{"level":50,"time":1762191187911,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/.umi/core/plugin.ts:5:30: ERROR: [plugin: esbuildAliasPlugin] Can't resolve '/Users/<USER>/0311 tango/tango/apps/playground/src/.umi/exports' in '/Users/<USER>/0311 tango/tango/apps/playground/src/.umi/core'\nsrc/.umi/umi.ts:10:33: ERROR: [plugin: esbuildAliasPlugin] Can't resolve '/Users/<USER>/0311 tango/tango/apps/playground/src/.umi/exports' in '/Users/<USER>/0311 tango/tango/apps/playground/src/.umi'\nsrc/layouts/index.tsx:2:23: ERROR: [plugin: esbuildAliasPlugin] Can't resolve '/Users/<USER>/0311 tango/tango/apps/playground/src/.umi/exports' in '/Users/<USER>/0311 tango/tango/apps/playground/src/layouts'"}
{"level":50,"time":1762191188368,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/core/plugin.ts:7:0-36"}
{"level":50,"time":1762191188368,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:14:0-39"}
{"level":50,"time":1762191188368,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"./src/layouts/index.tsx:5:0-29"}
{"level":55,"time":1762191188428,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiling..."}
{"level":50,"time":1762191188478,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/core/plugin.ts:7:0-36"}
{"level":50,"time":1762191188478,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"./src/.umi/umi.ts:14:0-39"}
{"level":50,"time":1762191188478,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"./src/layouts/index.tsx:5:0-29"}
{"level":55,"time":1762191188595,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiling..."}
{"level":50,"time":1762191188612,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"[icons] build failed: Error: Build failed with 3 errors:\nsrc/.umi/core/plugin.ts:5:30: ERROR: [plugin: esbuildAliasPlugin] Can't resolve '/Users/<USER>/0311 tango/tango/apps/playground/src/.umi/exports' in '/Users/<USER>/0311 tango/tango/apps/playground/src/.umi/core'\nsrc/.umi/umi.ts:10:33: ERROR: [plugin: esbuildAliasPlugin] Can't resolve '/Users/<USER>/0311 tango/tango/apps/playground/src/.umi/exports' in '/Users/<USER>/0311 tango/tango/apps/playground/src/.umi'\nsrc/layouts/index.tsx:2:23: ERROR: [plugin: esbuildAliasPlugin] Can't resolve '/Users/<USER>/0311 tango/tango/apps/playground/src/.umi/exports' in '/Users/<USER>/0311 tango/tango/apps/playground/src/layouts'"}
{"level":31,"time":1762191188788,"pid":96034,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://*************:8003\u001b[39m              \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1762191188911,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 291 ms (4193 modules)"}
{"level":32,"time":1762191189264,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 733 ms (4193 modules)"}
{"level":32,"time":1762191190463,"pid":96034,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 1689 ms (4193 modules)"}
{"level":32,"time":1762191822227,"pid":96034,"hostname":"saifeisideMac-Studio.local","msg":"config routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":32,"time":1762191822226,"pid":93626,"hostname":"saifeisideMac-Studio.local","msg":"config routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":32,"time":1762191822226,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"config routes, routes, routes, routes, routes changed, regenerate tmp files..."}
{"level":30,"time":1762191847342,"pid":3031,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[33m[你知道吗？] 全局数据存储，在 React 之外修改数据怎么办？试试一键上手 valtio，详见 https://umijs.org/docs/max/valtio\u001b[39m"}
{"level":30,"time":1762191847343,"pid":3031,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[36m\u001b[1mUmi v4.3.20\u001b[22m\u001b[39m"}
{"level":30,"time":1762191847639,"pid":3031,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":31,"time":1762191848318,"pid":3031,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://*************:8001\u001b[39m              \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1762191849828,"pid":3031,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 1520 ms (4193 modules)"}
{"level":32,"time":1762191900249,"pid":3031,"hostname":"saifeisideMac-Studio.local","msg":"config headScripts, externals changed, restart server..."}
{"level":30,"time":1762191900249,"pid":3031,"hostname":"saifeisideMac-Studio.local","msg":"Restart dev server with port 8001..."}
{"level":30,"time":1762191902680,"pid":3397,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[33m[你知道吗？] 你知道可以通过 UMI_ENV 定义多个环境的配置吗，详见 https://umijs.org/docs/guides/env-variables#umi_env\u001b[39m"}
{"level":30,"time":1762191902681,"pid":3397,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[36m\u001b[1mUmi v4.3.20\u001b[22m\u001b[39m"}
{"level":30,"time":1762191902963,"pid":3397,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":31,"time":1762191903452,"pid":3397,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://*************:8001\u001b[39m              \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1762191911058,"pid":3397,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 7616 ms (4945 modules)"}
{"level":55,"time":1762191966880,"pid":3397,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiling..."}
{"level":32,"time":1762191967284,"pid":3397,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 365 ms (4930 modules)"}
{"level":32,"time":1762192018459,"pid":3397,"hostname":"saifeisideMac-Studio.local","msg":"config routes, routes, routes, routes, routes, routes changed, regenerate tmp files..."}
