{"level":30,"time":1762187509409,"pid":82481,"hostname":"saifeisideMac-Studio.local","msg":"[你知道吗？] 想快速修改 html 模板、DIY 项目？试试编写项目级插件，详见 https://umijs.org/docs/guides/directory-structure#plugints"}
{"level":30,"time":1762187509410,"pid":82481,"hostname":"saifeisideMac-Studio.local","msg":"generate files"}
{"level":30,"time":1762187509700,"pid":82481,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":30,"time":1762187574581,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[33m[你知道吗？] dev 模式下访问 /__umi 路由，可以发现很多有用的内部信息。\u001b[39m"}
{"level":30,"time":1762187574582,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[36m\u001b[1mUmi v4.3.20\u001b[22m\u001b[39m"}
{"level":30,"time":1762187574859,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":55,"time":1762187575341,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"[HTTPS] Starting service in https mode..."}
{"level":60,"time":1762187575342,"pid":83106,"hostname":"saifeisideMac-Studio.local","err":{"type":"Error","message":"ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'","stack":"Error: ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'\n    at readFileSync (node:fs:442:20)\n    at createHttpsServer (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-utils/dist/https.js:114:39)\n    at async createServer (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-webpack/dist/server/server.js:197:14)\n    at async Proxy.dev (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-webpack/dist/dev.js:64:3)\n    at async Command.fn (/Users/<USER>/0311 tango/tango/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js:409:9)\n    at async Service.run (/Users/<USER>/0311 tango/tango/node_modules/@umijs/core/dist/service/service.js:328:15)\n    at async Service.run2 (/Users/<USER>/0311 tango/tango/node_modules/umi/dist/service/service.js:65:12)\n    at async /Users/<USER>/0311 tango/tango/node_modules/umi/dist/cli/forkedDev.js:23:5","errno":-2,"code":"ENOENT","syscall":"open","path":"/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem"},"msg":"ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'"}
{"level":60,"time":1762187575374,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1762187575375,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"/Users/<USER>/0311 tango/tango/apps/playground/node_modules/.cache/logger/umi.log"}
{"level":60,"time":1762187575376,"pid":83106,"hostname":"saifeisideMac-Studio.local","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1762187615612,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[33m[你知道吗？] father 4 正式发布了，详见 https://zhuanlan.zhihu.com/p/558192063\u001b[39m"}
{"level":30,"time":1762187615613,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[36m\u001b[1mUmi v4.3.20\u001b[22m\u001b[39m"}
{"level":30,"time":1762187615883,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":55,"time":1762187616369,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"[HTTPS] Starting service in https mode..."}
{"level":60,"time":1762187616370,"pid":83412,"hostname":"saifeisideMac-Studio.local","err":{"type":"Error","message":"ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'","stack":"Error: ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'\n    at readFileSync (node:fs:442:20)\n    at createHttpsServer (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-utils/dist/https.js:114:39)\n    at async createServer (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-webpack/dist/server/server.js:197:14)\n    at async Proxy.dev (/Users/<USER>/0311 tango/tango/node_modules/@umijs/bundler-webpack/dist/dev.js:64:3)\n    at async Command.fn (/Users/<USER>/0311 tango/tango/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js:409:9)\n    at async Service.run (/Users/<USER>/0311 tango/tango/node_modules/@umijs/core/dist/service/service.js:328:15)\n    at async Service.run2 (/Users/<USER>/0311 tango/tango/node_modules/umi/dist/service/service.js:65:12)\n    at async /Users/<USER>/0311 tango/tango/node_modules/umi/dist/cli/forkedDev.js:23:5","errno":-2,"code":"ENOENT","syscall":"open","path":"/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem"},"msg":"ENOENT: no such file or directory, open '/Users/<USER>/0311 tango/tango/apps/playground/local.netease.com-key.pem'"}
{"level":60,"time":1762187616418,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"A complete log of this run can be found in:"}
{"level":60,"time":1762187616419,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"/Users/<USER>/0311 tango/tango/apps/playground/node_modules/.cache/logger/umi.log"}
{"level":60,"time":1762187616421,"pid":83412,"hostname":"saifeisideMac-Studio.local","msg":"Consider reporting a GitHub issue on https://github.com/umijs/umi/issues"}
{"level":30,"time":1762187658346,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[33m[你知道吗？] 全局数据存储，在 React 之外修改数据怎么办？试试一键上手 valtio，详见 https://umijs.org/docs/max/valtio\u001b[39m"}
{"level":30,"time":1762187658347,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[36m\u001b[1mUmi v4.3.20\u001b[22m\u001b[39m"}
{"level":30,"time":1762187658626,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"Preparing..."}
{"level":31,"time":1762187659126,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"\u001b[90m\u001b[2m║\u001b[22m\u001b[39m  \u001b[90m>\u001b[39m Network: \u001b[32mhttp://*************:8001\u001b[39m              \u001b[90m\u001b[2m║\u001b[22m\u001b[39m"}
{"level":32,"time":1762187665285,"pid":83711,"hostname":"saifeisideMac-Studio.local","msg":"[Webpack] Compiled in 6168 ms (4208 modules)"}
